import 'package:aichat/api/server.dart';
import 'package:aichat/data/const.dart';
import 'package:aichat/ui/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:url_launcher/url_launcher.dart';

class ProfileController extends GetxController {
  final Server _server = Server();

  // 用户信息
  final RxString accountId = ''.obs;
  final RxString keyValue = ''.obs;
  final RxInt totalQuota = 0.obs;
  final RxInt remainingQuota = 0.obs;
  final RxInt usedQuota = 0.obs;

  // 密钥显示状态
  final RxBool isKeyVisible = false.obs;

  // 模型信息
  final RxList<dynamic> models = <dynamic>[].obs;
  final RxString currentModelName = ''.obs;

  // 卡密充值
  final TextEditingController cardCodeController = TextEditingController();
  final RxBool isLoading = false.obs;
  final RxString message = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  @override
  void onClose() {
    cardCodeController.dispose();
    super.onClose();
  }

  // 初始化
  Future _initData() async {
    await loadUserData(); // 加载用户数据
    await loadModels(); // 加载模型列表
  }

  // 加载用户数据
  Future<void> loadUserData() async {
    try {
      print('开始加载用户数据');
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      // 读取用户名和键ID
      accountId.value =
          await settingBox.get(ConstData.userName, defaultValue: '未登录');
      keyValue.value =
          await settingBox.get(ConstData.keyValue, defaultValue: '0').toString();

      // 读取额度信息
      totalQuota.value =
          await settingBox.get(ConstData.keyAllQuota, defaultValue: 0);
      usedQuota.value =
          await settingBox.get(ConstData.keyUsedQuota, defaultValue: 0);
      remainingQuota.value = totalQuota.value - usedQuota.value;
      print('结束加载用户数据');
    } catch (e) {
      print('加载用户数据错误: $e');
    }
  }

  // 加载模型列表
  Future<void> loadModels() async {
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    // 获取所有模型
    final allModels = await settingBox.get(ConstData.allModels);

    // 获取当前选中的模型
    Map savedModel = await settingBox.get(ConstData.currentModel);

    if (allModels != null) {
      // assignAll: 清楚原先所有列表并且再添加
      models.assignAll(allModels);

      print('当前获取的模型：${savedModel['name']}');

      // 如果当前已经有储存的模型
      for (var model in models) {
        if (model['name'] != null &&
            model['name'].toString() == savedModel['name']) {
          currentModelName.value = savedModel['name'];
          print('已找到用户之前选择的模型: ${model['name']} (Name: ${savedModel['name']})');
          break;
        }
      }
    }
    // 确保有最新模型列表
    await _server.getModelList();
  }

  // 更新当前选中的模型
  Future<void> setCurrentModel(Map model) async {
    try {
      // 更新当前选中的模型名称
      currentModelName.value = model['name'];

      // 保存到本地存储
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      // 保存模型名称
      await settingBox.put(ConstData.currentModel, model);
      print('已保存选中模型: ${model['name']}');
    } catch (e) {
      print('设置当前模型错误: $e');
    }
  }

  // 刷新卡密信息
  Future<void> refreshKeyInfo() async {
    try {
      await _server.getKeyInfo();
      await loadUserData();
    } catch (e) {
      print('刷新卡密信息错误: $e');
    }
  }

  // 切换密钥显示状态
  void toggleKeyVisibility() {
    isKeyVisible.value = !isKeyVisible.value;
  }

  // 获取显示的密钥值
  String get displayKeyValue {
    if (isKeyVisible.value) {
      return keyValue.value;
    } else {
      // 如果密钥长度小于8，全部用*替代
      if (keyValue.value.length <= 8) {
        return '*' * keyValue.value.length;
      }
      // 显示前4位和后4位，中间用*替代
      return '${keyValue.value.substring(0, 4)}${'*' * (keyValue.value.length - 8)}${keyValue.value.substring(keyValue.value.length - 4)}';
    }
  }

  // 添加卡密额度
  Future<void> addKeyQuota() async {
    if (cardCodeController.text.isEmpty) {
      message.value = '请输入卡密';
      return;
    }

    try {
      isLoading.value = true;
      message.value = '正在处理...';

      await _server.addKeyQuota(cardCodeController.text);

      // 清空输入并重新加载数据
      cardCodeController.clear();
      await refreshKeyInfo();

      message.value = '充值成功！';
    } catch (e) {
      message.value =
          '充值失败: ${e.toString().contains('Exception:') ? e.toString().split('Exception:')[1].trim() : e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // 打开付费网站
  // todo 设置从云端获取链接
  Future<void> openPaymentWebsite() async {
    const url = 'https://aichat.kyooo.top'; // 使用您的付费网站URL
    try {
      if (await canLaunchUrl(Uri(path: url))) {
        await canLaunchUrl(Uri(path: url));
      } else {
        throw '无法打开网站 $url';
      }
    } catch (e) {
      print('打开网站错误: $e');
    }
  }
}
