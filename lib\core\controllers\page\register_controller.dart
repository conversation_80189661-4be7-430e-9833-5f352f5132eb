import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../api/client.dart';
import '../../../data/models/api_defult_model.dart';
import '../../../ui/widgets/toast/toast.dart';

class RegisterController extends GetxController {
  // 表单控制器
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();

  // 倒计时
  RxInt count = 120.obs;
  late Timer _timer;

  // 复选框状态
  final RxBool agreeToTerms = false.obs;

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isSentCode = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();

    // 如果有参数传递过来，设置邮箱地址
    var arguments = Get.arguments;
    if (arguments != null && arguments is String) {
      emailController.text = arguments;
    }
  }

  // 表单验证
  bool validateEmail() {
    if (emailController.text.trim().isEmpty) {
      errorMessage.value = '请输入电子邮箱';
      hasError.value = true;
      return false;
    }

    // 简单的邮箱格式验证
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(emailController.text.trim())) {
      errorMessage.value = '请输入有效的电子邮箱';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 验证密码
  bool validatePassword() {
    if (passwordController.text.isEmpty) {
      errorMessage.value = '请输入密码';
      hasError.value = true;
      return false;
    }

    if (passwordController.text.length < 8) {
      errorMessage.value = '密码长度必须大于8位';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 验证确认密码
  bool validateConfirmPassword() {
    if (confirmPasswordController.text.isEmpty) {
      errorMessage.value = '请再次输入密码';
      hasError.value = true;
      return false;
    }

    if (confirmPasswordController.text != passwordController.text) {
      errorMessage.value = '两次输入的密码不一致';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 验证验证码
  bool validateVerificationCode() {
    if (verificationCodeController.text.isEmpty) {
      errorMessage.value = '请输入验证码';
      hasError.value = true;
      return false;
    }

    // 这里可以添加更复杂的验证码验证逻辑

    hasError.value = false;
    return true;
  }

  // 验证条款是否同意
  bool validateTerms() {
    if (!agreeToTerms.value) {
      errorMessage.value = '请同意服务条款和隐私政策';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 发送验证码
  void sendVerificationCode() async {
    // 验证所有表单
    if (!validateEmail() ||
        !validatePassword() ||
        !validateConfirmPassword() ||
        !validateTerms()) {
      return;
    }

    try {
      Client client = Client();
      isLoading.value = true;
      APIModel result = await client.register(emailController.text.trim());

      // 处理结果
      if (result.isSuccess) {
        // 清理错误信息
        hasError.value = false;
        errorMessage.value = '';
        // 验证码发送成功
        isSentCode.value = true;
        isLoading.value = false;

        // 发送邮箱成功弹窗
        Toast.showSuccess('邮箱已发送', '请及时检查您的邮箱');
        // 开启倒计时
        countDown();
      }
    } catch (e, s) {
      print('邮箱注册报错信息：$e,$s');
      hasError.value = true;
      errorMessage.value = '出现错误，请稍后重试';
      // 未发送邮箱
      isLoading.value = false;
      isSentCode.value = false;
    }
  }

  // 注册方法
  void register() async {
    // 验证所有表单
    if (!validateEmail() ||
        !validatePassword() ||
        !validateConfirmPassword() ||
        !validateVerificationCode() ||
        !validateTerms()) {
      return;
    }

    // 设置加载状态
    isLoading.value = true;
    hasError.value = false;

    try {
      // 发起验证邮箱请求
      Client client = Client();
      APIModel data = await client.verifyRegister(
          emailController.text.trim(),
          passwordController.text.trim(),
          verificationCodeController.text.trim());
      if (data.isSuccess) {
        // 成功注册
        hasError.value = false;
        errorMessage.value = '';
        isLoading.value = false;

        //  注册成功弹窗
        Toast.showSuccess('注册成功','请使用账号密码进行登录');
        //
        // 成功后跳转到登录页面或主页
        Get.offAllNamed('/Login');
      }
    } catch (e) {
      // 处理错误
      hasError.value = true;
      errorMessage.value = '注册失败，请稍后重试';
    } finally {
      isLoading.value = false;
    }
  }

  countDown() {
    print('init  $count');
    count.value = 120;
    _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (count.value >= 1) {
        count.value -= 1;
      } else {
        print('倒计时结束了');
        _timer.cancel();
        // 重置发送邮箱状态
        isSentCode.value = false;
      }
    });
  }

// @override
// void onClose() {
//   emailController.dispose();
//   passwordController.dispose();
//   confirmPasswordController.dispose();
//   verificationCodeController.dispose();
//   super.onClose();
// }
}
