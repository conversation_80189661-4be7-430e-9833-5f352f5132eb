import 'package:aichat/api/client.dart';
import 'package:aichat/api/until.dart';
import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:hive/hive.dart';

import '../../api/server.dart';
import '../../data/const.dart';
import '../widgets/toast/toast.dart';

class TestPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Padding(
      padding: const EdgeInsets.fromLTRB(20, 50, 20, 0),
      child: Wrap(
        children: [
          TextButton(
              onPressed: () {
                Toast.showSuccess('title', '');
              },
              child: const Text('登录成功弹窗')),
          TextButton(
              onPressed: () {
                Toast.showSuccess('登录成功', '开始愉快的聊天吧');              },
              child: const Text('注册成功弹窗')),
          TextButton(
              onPressed: () {
                Toast.showSuccess('邮箱已发送', '请及时检查您的邮箱');
              },
              child: const Text('发送邮箱弹窗')),
          TextButton(
              onPressed: () {
                Toast.showSuccess('请登录您的账号', '登录账号后才可以进行聊天');
              },
              child: const Text('登录账号弹窗')),
          const Divider(),
          TextButton(
            onPressed: () async {
              Client client = Client();
              await client.getPrompt();
            },
            child: const Text('获取云端提示词'),
          ),
          TextButton(
            onPressed: () async {
              Client client = Client();
              await client.getModels();
            },
            child: const Text('获取所有模型'),
          ),
          const Divider(),
          TextButton(
            onPressed: () async {
              Server server = Server();
              await server.getKeyInfo();
            },
            child: const Text('查询用户卡密信息'),
          ),
          const Divider(),
          TextButton(
            onPressed: () async {
              final Box settingBox = await Hive.openBox(ConstData.settingBox);
              await settingBox.put('isJine', true);
              print('修改成功');
            },
            child: const Text('修改自定义界面判断'),
          ),
          const Divider(),
          TextButton(
            onPressed: () async {
              CheckVersion versionChecker = CheckVersion();
              await versionChecker.checkForUpdates();
            },
            child: const Text('检查版本更新'),
          ),
          const Divider(),
          TextButton(
            onPressed: () async {
              Get.toNamed('/TestTheme');
            },
            child: const Text('主题测试'),
          ),
          TextButton(
            onPressed: () async {
              Get.toNamed('/TestMain');
            },
            child: const Text('界面测试'),
          ),
        ],
      ),
    ));
  }
}
