// {{ AURA-X: Add - 创建公告服务类，处理公告显示逻辑. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../api/client.dart';
import '../../data/const.dart';
import '../../data/models/announcement_response.dart';
import '../../ui/widgets/announcement/announcement_dialog.dart';

class AnnouncementService {
  static final Client _client = Client();

  /// 检查并显示公告
  /// 在应用启动时调用
  static Future<void> checkAndShowAnnouncements() async {
    try {
      // 检查是否应该显示公告
      if (!await _shouldShowAnnouncements()) {
        print('根据设置跳过公告显示');
        return;
      }

      // 获取公告数据
      final announcementResponse = await _client.getAnnouncements();
      
      if (announcementResponse == null || announcementResponse.announcements.isEmpty) {
        print('没有可显示的公告');
        return;
      }

      // {{ AURA-X: Add - 按时间排序公告，最新的在前. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      // 对公告按发布时间降序排列（最新的在前）
      announcementResponse.announcements.sort((a, b) =>
        b.createdDateTime.compareTo(a.createdDateTime));

      // 显示公告弹窗
      await _showAnnouncementDialog(announcementResponse);
      
    } catch (e) {
      print('检查公告时发生错误: $e');
    }
  }

  /// 判断是否应该显示公告
  /// 基于用户设置和上次显示时间
  static Future<bool> _shouldShowAnnouncements() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      // 检查是否启用公告显示（默认启用）
      bool isEnabled = await settingBox.get(ConstData.announcementEnabled, defaultValue: true);
      if (!isEnabled) {
        return false;
      }

      // 检查上次显示时间
      int? lastShowTime = await settingBox.get(ConstData.lastAnnouncementTime);
      
      if (lastShowTime == null) {
        // 首次使用，显示公告
        return true;
      }

      // 检查是否距离上次显示超过24小时（可根据需要调整）
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - lastShowTime;
      final hoursDifference = timeDifference / (1000 * 60 * 60);

      // 如果超过24小时，则显示公告
      return hoursDifference >= 24;
      
    } catch (e) {
      print('检查公告显示设置时发生错误: $e');
      // 出错时默认显示
      return true;
    }
  }

  /// 显示公告弹窗
  static Future<void> _showAnnouncementDialog(AnnouncementResponse announcementResponse) async {
    try {
      // 使用Get.dialog显示弹窗
      await Get.dialog(
        AnnouncementDialog(announcementResponse: announcementResponse),
        barrierDismissible: false, // 不允许点击外部关闭
      );
    } catch (e) {
      print('显示公告弹窗时发生错误: $e');
    }
  }

  /// 手动显示公告（用于设置页面等）
  static Future<void> showAnnouncementsManually() async {
    try {
      final announcementResponse = await _client.getAnnouncements();
      
      if (announcementResponse == null || announcementResponse.announcements.isEmpty) {
        // 可以显示一个提示，表示没有公告
        Get.snackbar(
          '提示',
          '当前没有可显示的公告',
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      // {{ AURA-X: Add - 手动显示时也按时间排序. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      // 对公告按发布时间降序排列（最新的在前）
      announcementResponse.announcements.sort((a, b) =>
        b.createdDateTime.compareTo(a.createdDateTime));

      await _showAnnouncementDialog(announcementResponse);
      
    } catch (e) {
      print('手动显示公告时发生错误: $e');
      Get.snackbar(
        '错误',
        '获取公告失败，请稍后重试',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  /// 设置公告显示状态
  static Future<void> setAnnouncementEnabled(bool enabled) async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);
      await settingBox.put(ConstData.announcementEnabled, enabled);
      print('公告显示设置已更新: $enabled');
    } catch (e) {
      print('更新公告显示设置时发生错误: $e');
    }
  }

  /// 获取公告显示状态
  static Future<bool> getAnnouncementEnabled() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);
      return await settingBox.get(ConstData.announcementEnabled, defaultValue: true);
    } catch (e) {
      print('获取公告显示设置时发生错误: $e');
      return true; // 默认启用
    }
  }

  /// 重置公告显示时间（用于测试或强制显示）
  static Future<void> resetAnnouncementTime() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);
      await settingBox.delete(ConstData.lastAnnouncementTime);
      print('公告显示时间已重置');
    } catch (e) {
      print('重置公告显示时间时发生错误: $e');
    }
  }
}
