// 设置不同的dio拦截器

import 'package:dio/dio.dart';

import '../../data/hive/curd.dart';
import '../../data/models/api_defult_model.dart';
import '../../ui/widgets/toast/toast.dart';

// 自定义未登录异常类型
class UnauthenticatedException extends DioException {
  UnauthenticatedException({
    required RequestOptions requestOptions,
    String? message,
  }) : super(
          requestOptions: requestOptions,
          message: message ?? '用户未登录',
          type: DioExceptionType.cancel, // 使用cancel类型来标识这是我们自定义的异常
        );
}

// 请求前传递过来的数据
class OnRequestInterceptor extends Interceptor {
  // 定义需要token的API端点
  static const List<String> _protectedEndpoints = [
    '/ai/models', // 获取模型列表
    '/ai', // 获取提示词
    '/key', // 密钥相关操作
    '/checkin', // {{ AURA-X: Add - 添加签到相关API端点. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
  ];

  // 检查是否为受保护的端点
  bool _isProtectedEndpoint(String path) {
    return _protectedEndpoints.any((endpoint) => path.startsWith(endpoint));
  }

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // print('=== REQUEST DEBUG INFO ===');
    // print('请求URL: ${options.baseUrl}${options.path}');
    // print('请求方法: ${options.method}');

    // 检查是否为受保护的端点
    bool isProtected = _isProtectedEndpoint(options.path);
    // print('是否为受保护端点: $isProtected');

    // 检查是否登录，是否存在token
    String? token = await getToken();

    // 如果是受保护的端点但没有token，直接终止请求并抛出自定义异常
    if (isProtected && (token == null || token.isEmpty)) {
      print('❌ 未登录状态下访问受保护端点，终止请求');
      print('========================');

      // 创建自定义的未登录异常
      final exception = UnauthenticatedException(
        requestOptions: options,
        message: '访问 ${options.path} 需要登录，请先登录后再试',
      );

      // 直接返回错误，不继续执行请求
      handler.reject(exception);
      return;
    }

    if (token != null && token.isNotEmpty) {
      // 添加Authorization头
      options.headers['Authorization'] = token;
      // print('✅ 已添加Authorization头');
    } else if (!isProtected) {
      // print('ℹ️ 公开端点，无需token');
    }

    // print('最终请求头: ${options.headers}');
    // print('========================');

    handler.next(options);
  }
}

// 请求后得到的数据
class OnResponseInterceptor extends Interceptor {
  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    // {{ AURA-X: Modify - 更新响应拦截器以匹配新API格式. Approval: 寸止(ID:2025-07-29T21:54:03+08:00). }}
    // 处理得到的信息，转换为api模型
    try {
      if (response.data is Map<String, dynamic> &&
          (response.data.containsKey('success') ||
              response.data.containsKey('code') ||
              response.data.containsKey('data'))) {
        response.data = APIModel.fromJson(response.data);
      }
    } catch (e) {
      print('响应数据转换错误: $e');
    }

    handler.next(response);
  }
}

// 报错拦截
class ErrorInterceptor extends Interceptor {
  @override
  void onError(
    DioException e,
    ErrorInterceptorHandler handler,
  ) {
    print('=== ERROR DEBUG INFO ===');
    print('错误类型: ${e.type}');
    print('请求URL: ${e.requestOptions.uri}');
    print('请求方法: ${e.requestOptions.method}');
    print('请求头: ${e.requestOptions.headers}');
    print('请求体: ${e.requestOptions.data}');


    // {{ AURA-X: Modify - 修复空值处理，避免类型转换错误. Approval: 寸止(ID:2025-08-03T15:30:00+08:00). }}
    // 设置弹窗显示报错信息
    if (e.response?.data != null && e.response?.data is Map) {
      final responseData = e.response!.data as Map<String, dynamic>;
      final message = responseData['message']?.toString() ?? '请求失败';
      final details = responseData['details']?.toString() ?? '未知错误';
      Toast.showError(message, details);
    } else if (e.response != null) {
      // 如果有响应但数据格式不正确，显示状态码信息
      Toast.showError('请求失败', '服务器返回错误 (${e.response!.statusCode})');
    } else {
      // 如果没有响应，显示网络错误信息
      Toast.showError('网络错误', '无法连接到服务器，请检查网络连接');
    }

    // 特别处理自定义的未登录异常
    if (e is UnauthenticatedException) {
      print('🔒 检测到未登录访问受保护资源');
      print('错误信息: ${e.message}');
      print('=======================');

      // 对于未登录错误，我们不继续传递异常，而是静默处理
      // 可以在这里添加自定义的处理逻辑，比如显示登录提示等
      print('ℹ️ 未登录错误已被拦截，不会抛出到上层');

      // {{ AURA-X: Modify - 更新自定义响应以匹配新API格式. Approval: 寸止(ID:2025-07-29T21:54:03+08:00). }}
      // 创建一个自定义的响应，表示需要登录
      final response = Response(
        requestOptions: e.requestOptions,
        statusCode: 401,
        data: APIModel(
          success: false,
          code: 401,
          message: '需要登录才能访问此资源',
          data: null,
        ),
      );



      // 返回自定义响应而不是错误
      handler.resolve(response);
      return;
    }

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        // 连接超时处理
        // todo 尝试重试
        print('连接超时');
        break;
      case DioExceptionType.sendTimeout:
        // 发送超时处理
        // todo 尝试重试
        print('发送超时');
        break;
      case DioExceptionType.receiveTimeout:
        // 接收超时处理
        print('接收超时');
        break;
      case DioExceptionType.badResponse:
        // 服务器响应错误处理
        print('服务器响应错误，状态码：${e.response?.statusCode}');
        print('响应数据: ${e.response?.data}');
        print('响应头: ${e.response?.headers}');

        // 特别处理404错误
        if (e.response?.statusCode == 404) {
          print('404错误详情:');
          print('- 请求的URL可能不存在');
          print('- 检查Authorization头是否正确');
          print('- 检查token格式是否正确');
        }
        break;
      case DioExceptionType.cancel:
        // 请求取消处理
        print('请求被取消');
        break;
      case DioExceptionType.connectionError:
        // 连接错误处理
        print('连接错误: ${e.message}');
        break;
      case DioExceptionType.unknown:
      default:
        // 其他错误处理
        print('未知错误: ${e.message}');
        break;
    }
    print('=======================');

    handler.next(e);
  }
}
