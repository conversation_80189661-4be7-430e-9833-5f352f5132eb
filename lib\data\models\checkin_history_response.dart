// {{ AURA-X: Add - 创建签到历史响应数据模型类. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
import 'checkin_model.dart';

class CheckinHistoryResponse {
  final List<CheckinModel> history;
  final int totalCheckinDays;
  final double totalRewards;

  CheckinHistoryResponse({
    required this.history,
    required this.totalCheckinDays,
    required this.totalRewards,
  });

  factory CheckinHistoryResponse.fromJson(Map<String, dynamic> json) {
    var historyList = json['history'] as List? ?? [];
    List<CheckinModel> history = historyList
        .map((item) => CheckinModel.fromJson(item))
        .toList();

    return CheckinHistoryResponse(
      history: history,
      totalCheckinDays: json['total_checkin_days'] ?? 0,
      totalRewards: (json['total_rewards'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'history': history.map((item) => item.toJson()).toList(),
      'total_checkin_days': totalCheckinDays,
      'total_rewards': totalRewards,
    };
  }

  // 获取格式化的总奖励
  String get formattedTotalRewards {
    return '${totalRewards.toStringAsFixed(2)}额度';
  }

  // 获取连续签到天数（基于最近的签到记录）
  int get consecutiveDays {
    if (history.isEmpty) return 0;

    // 按日期排序（最新的在前）
    final sortedHistory = List<CheckinModel>.from(history);
    sortedHistory.sort((a, b) => b.checkinDate.compareTo(a.checkinDate));

    int consecutive = 0;
    DateTime? lastDate;

    for (var checkin in sortedHistory) {
      try {
        final currentDate = DateTime.parse(checkin.checkinDate);

        if (lastDate == null) {
          // 第一条记录
          consecutive = 1;
          lastDate = currentDate;
        } else {
          // 检查是否连续
          final difference = lastDate.difference(currentDate).inDays;
          if (difference == 1) {
            consecutive++;
            lastDate = currentDate;
          } else {
            break; // 不连续，停止计算
          }
        }
      } catch (e) {
        break; // 日期解析错误，停止计算
      }
    }

    return consecutive;
  }

  // 检查今日是否已签到
  bool get hasCheckedInToday {
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

    return history.any((checkin) => checkin.checkinDate == todayString);
  }

  // 获取今日签到记录
  CheckinModel? get todayCheckin {
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

    try {
      return history.firstWhere((checkin) => checkin.checkinDate == todayString);
    } catch (e) {
      return null;
    }
  }
}
