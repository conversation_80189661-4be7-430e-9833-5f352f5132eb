import 'package:aichat/api/client.dart';
import 'package:hive/hive.dart';

import '../data/const.dart';
import '../data/hive/curd.dart';
import '../data/models/api_defult_model.dart';

class Server {
  Client client = Client();

  // 获取用户卡密的所有信息
  Future<void> getKeyInfo() async {
    // 从本地获取相关的信息
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    String userName = await settingBox.get(ConstData.userName);
    String key = await settingBox.get(ConstData.keyValue);

    // 获取用户卡密信息
    APIModel data = await client.getKeyInfo(userName, key.replaceAll('sk-', ''));
    // 储存卡密信息
    saveKeyInfo(data.data!);
  }

  // 获取所有模型列表
  Future<void> getModelList() async {
    await client.getModels();
  }

  // 获取云端的提示词
  Future<void> getPrompt() async {
    await client.getPrompt();
  }

  // 获取软件更新信息
  Future<void> getVersion() async {}

  // 验证卡密信息
  Future<void> addKeyQuota(String cord) async {
    // 从本地信息里面读取账户信息和id
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    String key = await settingBox.get(ConstData.keyValue);
    String username = await settingBox.get(ConstData.userName);
    // 尝试添加额度
    await client.addKeyQuota(username, key, cord);
    // 刷新个人界面的额度
  }
}
