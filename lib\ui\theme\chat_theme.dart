import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../data/const.dart';

/// 聊天主题配置类
/// 这个类定义了一个聊天界面的所有视觉配置
class ChatThemeConfig {
  /// 主题名称
  final String name;

  /// AppBar相关配置
  final Color? appBarColor;
  final double appBarHeight;

  /// 背景配置
  final String? backgroundImage;
  final Color? backgroundColor;

  /// 消息气泡配置
  final Color receiverBubbleColor;
  final Color senderBubbleColor;
  final Color receiverTextColor;
  final Color senderTextColor;
  final double bubbleRadius;

  /// 字体配置
  final String? fontFamily;
  final double fontSize;

  /// 输入区域配置
  final String? inputBackgroundImage;
  final bool showEmojiBar;

  /// 阴影配置
  final List<BoxShadow>? bubbleShadows;
  final List<BoxShadow>? avatarShadows;

  final Color? inputBackgroundColor;
  final Color? inputBorderColor;
  final Color? inputHintColor;
  final Color? inputTextColor;
  final Color? inputIconsColor;
  final Color? inputSendIconColor;
  final Color? inputStopIconColor;
  final List<BoxShadow>? inputBoxShadow;

  const ChatThemeConfig({
    required this.name,
    this.appBarColor,
    this.appBarHeight = 30,
    this.backgroundImage,
    this.backgroundColor,
    required this.receiverBubbleColor,
    required this.senderBubbleColor,
    required this.receiverTextColor,
    required this.senderTextColor,
    this.bubbleRadius = 14.0,
    this.fontFamily,
    this.fontSize = 15,
    this.inputBackgroundImage,
    this.showEmojiBar = false,
    this.bubbleShadows,
    this.avatarShadows,
    this.inputBackgroundColor,
    this.inputBorderColor,
    this.inputHintColor,
    this.inputTextColor,
    this.inputIconsColor,
    this.inputSendIconColor,
    this.inputStopIconColor,
    this.inputBoxShadow,
  });

  /// 创建默认主题
  /// 这个主题使用系统的颜色方案，适配黑白主题
  factory ChatThemeConfig.defaultTheme() {
    final colorScheme = Get.theme.colorScheme;

    return ChatThemeConfig(
      name: 'default',
      receiverBubbleColor: colorScheme.surfaceContainer,
      senderBubbleColor: colorScheme.primaryContainer,
      receiverTextColor: colorScheme.onSurface,
      senderTextColor: colorScheme.onPrimaryContainer,
      bubbleRadius: 14.0,
      fontSize: 15,
      showEmojiBar: false,
      fontFamily: 'LXGW',
      bubbleShadows: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
      avatarShadows: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.2),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
      inputBackgroundColor: colorScheme.surfaceContainer,
      inputBorderColor: colorScheme.outline.withOpacity(0.3),
      inputHintColor: colorScheme.onSurfaceVariant,
      inputTextColor: colorScheme.onSurface,
      inputIconsColor: colorScheme.onPrimaryContainer,
      inputSendIconColor: colorScheme.primary,
      inputStopIconColor: colorScheme.error,
      inputBoxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// 创建Jine主题
  /// 这个主题有特定的颜色、字体和背景图片
  factory ChatThemeConfig.jineTheme() {
    return const ChatThemeConfig(
      name: 'jine',
      appBarColor: Color(0xFFee98ee),
      backgroundImage: 'assets/background/jine.png',
      inputBackgroundImage: 'assets/background/jine_emoji_bg.png',
      receiverBubbleColor: Color(0xFFcce4fc),
      senderBubbleColor: Color(0xFF53ee4a),
      receiverTextColor: Colors.black87,
      senderTextColor: Colors.black87,
      bubbleRadius: 7.0,
      fontFamily: 'Zpix',
      fontSize: 15,
      showEmojiBar: true,
      inputBackgroundColor: Color(0xFFfce4ec),
      inputBorderColor: Color(0xFFf8bbd0),
      inputHintColor: Colors.black54,
      inputTextColor: Colors.black87,
      inputIconsColor: Color(0xFF880e4f),
      inputSendIconColor: Color(0xFF53ee4a),
      inputStopIconColor: Colors.redAccent,
    );
  }

  /// 创建轻松熊主题
  /// 温暖可爱的棕色系主题
  factory ChatThemeConfig.bearTheme() {
    return const ChatThemeConfig(
      name: 'bear',
      appBarColor: Color(0xFFD2B48C),
      backgroundImage: 'assets/background/bear/background.jpg', 
      receiverBubbleColor: Color(0xFFFFF8DC),
      senderBubbleColor: Color(0xFFDEB887),
      receiverTextColor: Color(0xFF8B4513),
      senderTextColor: Color(0xFF654321),
      bubbleRadius: 12.0,
      fontSize: 15,
      fontFamily: 'LXGW',
      showEmojiBar: false,
      inputBackgroundColor: Color(0xFFF5EFE6),
      inputBorderColor: Color(0xFFDEB887),
      inputHintColor: Color(0xFF8B4513),
      inputTextColor: Color(0xFF654321),
      inputIconsColor: Color(0xFF8B4513),
      inputSendIconColor: Color(0xFF8B4513),
      inputStopIconColor: Color(0xFFCD5C5C),
    );
  }

  /// 创建酷洛米主题
  /// 优雅的紫白色系主题
  factory ChatThemeConfig.kuromiTheme() {
    return const ChatThemeConfig(
      name: 'kuromi',
      appBarColor: Color(0xFF9370DB),
      backgroundImage: 'assets/background/kuromi/background.png',
      receiverBubbleColor: Color(0xFFF8F8FF),
      senderBubbleColor: Color(0xFFE6E6FA),
      receiverTextColor: Color(0xFF4B0082),
      senderTextColor: Color(0xFF663399),
      bubbleRadius: 10.0,
      fontFamily: 'LXGW',
      fontSize: 15,
      showEmojiBar: false,
      inputBackgroundColor: Color(0xFFE6E6FA),
      inputBorderColor: Color(0xFF9370DB),
      inputHintColor: Color(0xFF663399),
      inputTextColor: Color(0xFF4B0082),
      inputIconsColor: Color(0xFF4B0082),
      inputSendIconColor: Color(0xFF9370DB),
      inputStopIconColor: Color(0xFFC71585),
    );
  }
}

/// 聊天主题管理器
/// 负责管理所有的聊天主题，并根据角色ID或主题名称提供对应的主题配置
class ChatThemeManager {

  /// 获取主题的显示信息
  /// 返回包含主题显示名称、描述和预览图片的信息
  static Map<String, dynamic> getThemeDisplayInfo(String themeName) {
    switch (themeName) {
      case 'default':
        return {
          'displayName': '默认主题',
          'description': '跟随系统的简洁主题',
          'previewImage': 'assets/background/jine.png',
          'characterOnly': null,
        };
      case 'jine':
        return {
          'displayName': 'JINE主题',
          'description': '专为角色"糖糖"设计的粉色可爱主题',
          'previewImage': 'assets/background/jine.png',
          'characterOnly': '糖糖',
        };
      case 'bear':
        return {
          'displayName': '轻松熊主题',
          'description': '温暖可爱的棕色系主题，给人温馨的感觉',
          'previewImage': 'assets/background/bear/background.jpg',
          'characterOnly': null,
        };
      case 'kuromi':
        return {
          'displayName': '酷洛米主题',
          'description': '优雅的紫白色系主题，简约而不失个性',
          'previewImage': 'assets/background/kuromi/background.png',
          'characterOnly': null,
        };
      default:
        return {
          'displayName': '默认主题',
          'description': '跟随系统的简洁主题',
          'previewImage': null,
          'characterOnly': null,
        };
    }
  }

  /// 从本地读取储存的主题信息
  /// 返回主题配置信息
  static Future<ChatThemeConfig> loadTheme() async {
    // 储存hive的bot
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    String themeName =
        await settingBox.get(ConstData.themeSaveName) ?? 'default';
    print('当前读取的主题名称：$themeName');

    switch (themeName) {
      case 'default':
        return ChatThemeConfig.defaultTheme();
      case 'kuromi':
        return ChatThemeConfig.kuromiTheme();
      case 'bear':
        return ChatThemeConfig.bearTheme();
      case 'jine':
        return ChatThemeConfig.jineTheme();
      default:
        return ChatThemeConfig.defaultTheme();
    }
  }

  /// 储存主题配置信息到本地
  /// [themeName]：需要储存的主题配置名称
  static saveTheme(String themeName) async {
    // 储存hive的bot
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    await settingBox.put(ConstData.themeSaveName, themeName);
    print('当前储存的主题名称：$themeName');
  }
}
