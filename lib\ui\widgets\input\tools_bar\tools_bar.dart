import 'package:aichat/ui/widgets/input/tools_bar/restart.dart';
import 'package:flutter/material.dart';

class ToolsBar extends StatelessWidget {
  const ToolsBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
      width: MediaQuery.of(context).size.width,
      height: 150,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2), // 半透明背景色，增强视觉效果
          borderRadius: BorderRadius.circular(8), // 可选：添加圆角
        ),
        child: Wrap(
          alignment: WrapAlignment.start, // 从右侧开始排列
          children: [
            Restart(),
            // ChangePage()
            // 可在这里添加更多工具按钮
          ],
        ),
      ),
    );
  }
}
