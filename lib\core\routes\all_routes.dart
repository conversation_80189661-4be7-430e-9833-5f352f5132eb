import 'package:aichat/ui/screens/account/login.dart';
import 'package:aichat/ui/screens/account/register.dart';
import 'package:aichat/ui/screens/settings/setting.dart';
import 'package:get/get.dart';

import '../../ui/screens/account/forget.dart';
import '../../ui/screens/chats/chat_screen.dart';
import '../../ui/screens/checkin/checkin_calendar_page.dart';
import '../controllers/page/login_controller.dart';
import '../controllers/page/register_controller.dart';
import '../controllers/checkin_controller.dart';

class AllRoutes {
  static final allRoutes = [
    GetPage(
      name: '/Chat',
      page: () =>  ChatScreen(),
      // middlewares: [AuthMiddleware()],
    ),
    // GetPage(
    //   // 用于搭配中届器，达到自动选择界面的目的
    //   name: '/ChoosePage',
    //   page: () => const ChatPage(),
    //   middlewares: [AuthMiddleware()],
    // ),
    GetPage(
      name: '/Setting',
      page: () => SettingPage(),
    ),
    GetPage(
      name: '/Login',
      page: () => const LoginPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<LoginController>(() => LoginController());
      }),
    ),
    GetPage(
      name: '/Forget',
      page: () => const ForgetPasswordPage(),
    ),
    GetPage(
      name: '/Register',
      page: () => const Register(),
      binding: BindingsBuilder(() {
        Get.lazyPut<RegisterController>(() => RegisterController());
      }),
    ),
    // {{ AURA-X: Add - 添加签到页面路由. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
    GetPage(
      name: '/Checkin',
      page: () => CheckinCalendarPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<CheckinController>(() => CheckinController());
      }),
    ),
  ];
}
