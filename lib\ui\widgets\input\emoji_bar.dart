import 'package:aichat/core/controllers/messages_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';

import '../../../core/controllers/page/login_controller.dart';
import '../../../data/const.dart';

class EmojiBar extends GetView<MessagesController> {
  /// 使用GetView获取信息列表的控制器，MessagesController，确保发送消息的时候调用的控制器和界面显示的控制器是同一个

  EmojiBar({super.key});

  /// 点击图片之后会在聊天界面中显示对应的图片，且保存为图片地址
  /// 在AI聊天记忆中应该传递特定文本

  // 检测用户是否登录
  final LoginController _loginController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              children: [
                _buildEmojiRow(0, 4),
                _buildEmojiRow(4, 8),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmojiRow(int start, int end) {
    List<Widget> emojiButtons = [];

    // skip与take介绍：https://cloud.tencent.com/developer/article/1759865
    // https://juejin.cn/post/6844903988052099079#heading-39
    // map中的entries介绍：https://juejin.cn/post/7034299322636173319#heading-19
    ConstData.jineEmojiText.entries
        .skip(start) // 跳过几个数值
        .take(end - start) //
        .forEach((entries) {
      var emojiText = entries.key;
      var emojiScr = entries.value;
      emojiButtons.add(Container(
        padding: const EdgeInsets.all(8),
        child: GestureDetector(
          behavior: HitTestBehavior.opaque, // 优化点击响应区域
          onTap: () => _handleEmojiTap(emojiText),
          child: Image.asset(
            emojiScr,
            fit: BoxFit.contain,
          ),
        ),
      ));
    });

    return Row(mainAxisSize: MainAxisSize.min, children: emojiButtons);
  }

  void _handleEmojiTap(String emojiText) {
    if (_loginController.isLogin.value == false) {
      Get.toNamed(ConstData.loginPage);
    } else if (controller.isSending.value != true) {

      // ai正在回复中
      controller.isSending.value = true;

      MessageModel message = MessageModel(
          ownerType: OwnerType.sender,
          content: emojiText,
          createdAt: ConstData.getTime());
      controller.addMessage(message);
      print('点击表情: $emojiText');
    }
  }
}
