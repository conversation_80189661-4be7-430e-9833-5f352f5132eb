import 'models/character_setting.dart';

class ConstData {
  // 所有角色的配置信息
  static List<AICharacter> allCharacters = [
    AICharacter(
      id: 0,
      isCustom: true,
      name: '糖糖',
      nameEN: 'ame',
      image: 'assets/views/tweet_selfie_ame_home_002.png',
      description: """我的幸福既无虚伪，也不会令人不安
\n糖糖的人生也是有意义的
\n我有好好地活过""",
      pageHiveKey: ConstData.isJine,
      customPageName: ConstData.jinePage,
    )
  ];

  // 所有键值对均使用该id的box
  static const String settingBox = 'setting_box';

  // 存储 prompt 的键名
  static const String promptKey = 'prompt';
  // 存储 token 的键名
  static const String tokenKey = 'user_token';

  // key 的值名 默认带sk-前缀
  static const String keyValue = 'key_value';
  // key 总额度
  static const String keyAllQuota = 'key_all_quota';
  // key 剩余额度
  static const String keyUsedQuota = 'key_used_quota';

  // 登录状态的键名
  static const String isLogin = 'is_login';
  // ai交互的 baseurl 键名
  static const String baseUrl = 'base_url';
  // 用户名 的键名
  static const String userName = 'user_name';

  // ai 模型相关键名
  // 当前使用的模型
  static const String currentModel = 'current_model';
  // 本地储存的所有模型以及其信息，为map类型
  static const String allModels = 'all_models';

  // GetPage 相关部分
  static const String jinePage = '/Jine';
  static const String loginPage = '/Login';
  static const String registerPage = '/Register';
  static const String forgetPage = '/Forget';
  static const String profilePage = '/Profile';
  static const String homePage = '/Home';
  static const String settingPage = '/Setting';

  // 界面选择器，用户点击聊天卡片后，会判断当前的界面是自定义还是通用
  static const String isJine = 'isJine';

  // 主题相关
  // 储存主题名称的键名
  static const String themeSaveName = 'theme_save_name';

  // 公告相关
  // 储存上次显示公告时间的键名
  static const String lastAnnouncementTime = 'last_announcement_time';
  // 储存是否启用公告显示的键名
  static const String announcementEnabled = 'announcement_enabled';

  // 用于匹配发送给AI的文本，如果key相符合，那么界面就会以图片形式发送图片地址
  // 为了避免用户输入的文本与key雷同而误发图片，特意为每一个key末尾都添加一个英文标点以做出区分
  static Map jineEmojiText = {
    '嘤嘤嘤.': 'assets/emojis/cry.png',
    '原地去世.': 'assets/emojis/die.png',
    '关我屁事.': 'assets/emojis/idc.png',
    '永远爱你.': 'assets/emojis/love.png',
    'okk.': 'assets/emojis/ok.png',
    '好强~.': 'assets/emojis/omg.png',
    '抱歉.': 'assets/emojis/sorry.png',
    '你说得对.': 'assets/emojis/this.png',
  };

  static getTime() {
    return DateTime.now().millisecondsSinceEpoch;
  }
}
