// {{ AURA-X: Add - 创建美观的公告显示弹窗组件. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../../data/const.dart';
import '../../../data/models/announcement_model.dart';
import '../../../data/models/announcement_response.dart';

class AnnouncementDialog extends StatefulWidget {
  final AnnouncementResponse announcementResponse;

  const AnnouncementDialog({
    Key? key,
    required this.announcementResponse,
  }) : super(key: key);

  @override
  State<AnnouncementDialog> createState() => _AnnouncementDialogState();
}

class _AnnouncementDialogState extends State<AnnouncementDialog> {
  int currentIndex = 0;
  bool dontShowAgain = false;

  @override
  Widget build(BuildContext context) {
    final announcements = widget.announcementResponse.announcements;
    
    if (announcements.isEmpty) {
      return const SizedBox.shrink();
    }

    final currentAnnouncement = announcements[currentIndex];

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(currentAnnouncement),
            _buildContent(currentAnnouncement),
            if (announcements.length > 1) _buildPageIndicator(announcements.length),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(AnnouncementModel announcement) {
    return Container(
      padding: const EdgeInsets.all(16), // {{ AURA-X: Modify - 减小头部内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      decoration: BoxDecoration(
        color: _getTypeColor(announcement.type).withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6), // {{ AURA-X: Modify - 减小图标容器内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            decoration: BoxDecoration(
              color: _getTypeColor(announcement.type),
              borderRadius: BorderRadius.circular(6), // {{ AURA-X: Modify - 减小圆角. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            ),
            child: Text(
              announcement.typeIcon,
              style: const TextStyle(fontSize: 16), // {{ AURA-X: Modify - 减小图标字体大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            ),
          ),
          const SizedBox(width: 10), // {{ AURA-X: Modify - 减小间距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      announcement.typeDisplayName,
                      style: TextStyle(
                        color: _getTypeColor(announcement.type),
                        fontSize: 11, // {{ AURA-X: Modify - 减小类型标签字体. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    // {{ AURA-X: Add - 添加时间显示. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                    const SizedBox(width: 8),
                    Text(
                      announcement.formattedCreatedAt,
                      style: TextStyle(
                        color: Get.theme.colorScheme.onSurfaceVariant,
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2), // {{ AURA-X: Modify - 减小垂直间距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                Text(
                  announcement.title,
                  style: TextStyle(
                    color: Get.theme.colorScheme.onSurface,
                    fontSize: 16, // {{ AURA-X: Modify - 减小标题字体大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            iconSize: 20, // {{ AURA-X: Modify - 减小关闭按钮图标大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            padding: const EdgeInsets.all(4), // {{ AURA-X: Modify - 减小按钮内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            constraints: const BoxConstraints(), // {{ AURA-X: Modify - 移除默认约束. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            icon: Icon(
              Icons.close,
              color: Get.theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(AnnouncementModel announcement) {
    return Expanded(
      flex: 3, // {{ AURA-X: Modify - 增加内容区域的权重占比. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16), // {{ AURA-X: Modify - 优化内边距，增加垂直空间. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
        child: SingleChildScrollView(
          child: Text(
            announcement.content,
            style: TextStyle(
              color: Get.theme.colorScheme.onSurface,
              fontSize: 15, // {{ AURA-X: Modify - 稍微减小字体但保持可读性. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
              height: 1.5, // {{ AURA-X: Modify - 稍微减小行高以节省空间. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int totalPages) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // {{ AURA-X: Modify - 减小分页指示器内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: currentIndex > 0 ? _previousPage : null,
            iconSize: 20, // {{ AURA-X: Modify - 减小左右切换按钮图标大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            padding: const EdgeInsets.all(8), // {{ AURA-X: Modify - 减小按钮内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36), // {{ AURA-X: Modify - 减小按钮最小尺寸. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            icon: Icon(
              Icons.chevron_left,
              color: currentIndex > 0
                  ? Get.theme.colorScheme.primary
                  : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
            ),
          ),
          Row(
            children: List.generate(
              totalPages,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 3), // {{ AURA-X: Modify - 减小指示点间距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                width: 6, // {{ AURA-X: Modify - 减小指示点大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                height: 6, // {{ AURA-X: Modify - 减小指示点大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                decoration: BoxDecoration(
                  color: index == currentIndex
                      ? Get.theme.colorScheme.primary
                      : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(3), // {{ AURA-X: Modify - 调整圆角. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: currentIndex < totalPages - 1 ? _nextPage : null,
            iconSize: 20, // {{ AURA-X: Modify - 减小右切换按钮图标大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            padding: const EdgeInsets.all(8), // {{ AURA-X: Modify - 减小按钮内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36), // {{ AURA-X: Modify - 减小按钮最小尺寸. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            icon: Icon(
              Icons.chevron_right,
              color: currentIndex < totalPages - 1
                  ? Get.theme.colorScheme.primary
                  : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16), // {{ AURA-X: Modify - 减小底部操作区域内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
      child: Column(
        children: [
          Row(
            children: [
              Transform.scale(
                scale: 0.9, // {{ AURA-X: Modify - 缩小复选框尺寸. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                child: Checkbox(
                  value: dontShowAgain,
                  onChanged: (value) {
                    setState(() {
                      dontShowAgain = value ?? false;
                    });
                  },
                  activeColor: Get.theme.colorScheme.primary,
                ),
              ),
              Expanded(
                child: Text(
                  '下次更新后再显示',
                  style: TextStyle(
                    color: Get.theme.colorScheme.onSurfaceVariant,
                    fontSize: 13, // {{ AURA-X: Modify - 减小复选框文字大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12), // {{ AURA-X: Modify - 减小垂直间距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _handleClose,
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.colorScheme.primary,
                foregroundColor: Get.theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 12), // {{ AURA-X: Modify - 减小按钮垂直内边距. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10), // {{ AURA-X: Modify - 稍微减小按钮圆角. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                ),
              ),
              child: const Text(
                '我知道了',
                style: TextStyle(
                  fontSize: 15, // {{ AURA-X: Modify - 稍微减小按钮文字大小. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'warning':
        return Colors.orange;
      case 'urgent':
        return Colors.red;
      case 'info':
      default:
        return Get.theme.colorScheme.primary;
    }
  }

  void _previousPage() {
    if (currentIndex > 0) {
      setState(() {
        currentIndex--;
      });
    }
  }

  void _nextPage() {
    if (currentIndex < widget.announcementResponse.announcements.length - 1) {
      setState(() {
        currentIndex++;
      });
    }
  }

  void _handleClose() async {
    if (dontShowAgain) {
      // 保存设置，下次更新后再显示
      await _saveDontShowAgainSetting();
    }
    Get.back();
  }

  Future<void> _saveDontShowAgainSetting() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);
      
      // 保存当前时间戳，用于判断是否为新版本更新
      await settingBox.put(ConstData.lastAnnouncementTime, DateTime.now().millisecondsSinceEpoch);
      print('已保存公告显示设置');
    } catch (e) {
      print('保存公告设置失败: $e');
    }
  }
}
