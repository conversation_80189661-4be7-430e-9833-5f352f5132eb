class APIModel {
  final bool success;
  final int code;
  final String message;
  final String? errorCode;  // 失败时的错误代码
  final dynamic details;    // 失败时的详细信息
  final dynamic data;       // 成功时的数据
  final String? timestamp;  // API响应时间戳

  APIModel({
    required this.success,
    required this.code,
    required this.message,
    this.errorCode,
    this.details,
    this.data,
    this.timestamp,
  });

  factory APIModel.fromJson(Map<String, dynamic> json) => APIModel(
      success: json['success'] ?? false,
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      errorCode: json['error_code'],
      details: json['details'],
      data: json['data'],
      timestamp: json['timestamp']);

  // {{ AURA-X: Add - 添加便捷方法判断请求是否成功. Approval: 寸止(ID:2025-07-29T21:54:03+08:00). }}
  bool get isSuccess => success && code == 200;

  // {{ AURA-X: Add - 添加便捷方法获取错误信息. Approval: 寸止(ID:2025-07-29T21:54:03+08:00). }}
  String get errorMessage => success ? message : (message.isNotEmpty ? message : '请求失败');
}
