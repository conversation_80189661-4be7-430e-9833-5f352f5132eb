import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_rx/src/rx_workers/rx_workers.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:hive/hive.dart';

import '../../data/const.dart';
import '../../data/models/character_setting.dart';

class CharacterController extends GetxController {
  // 主页部分的位置，方便其他调用
  RxInt currentIndex = 0.obs;

  // 当前进入的角色界面，其中id是根据选择卡片里面设定的来
  // 唯一用于gpt破限确定使用那个角色的破限词
  // 云端：json传递角色id与对应的prompt
  // 本地：角色id对应不同的界面，以及对应获取云端prompt，还用于判断该角色是否有自定义界面
  RxInt currentCharacterId = 0.obs;

  final RxMap<int, AICharacter> characterSettings = <int, AICharacter>{}.obs;

  // 检测当前角色是否选择了自定义或者默认的界面
  RxBool get isCurrentCharOnCustomPage =>
      characterSettings[currentCharacterId.value]?.isUsedCustomPage ??
      false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeCharacterSettings();

    // 根据初始的 character id 加载设置
    await loadPageSettingsForCurrentCharacter();

    // 监听 characterId 的变化，自动加载新角色的设置
    ever(currentCharacterId,
        (_) async => await loadPageSettingsForCurrentCharacter());
  }

  /// 初始化所有角色的基础设置
  /// 未来可以从配置文件或服务器动态加载
  Future<void> _initializeCharacterSettings() async {
    // 按照循序添加不同的角色，其中顺序就是角色id
    characterSettings.assignAll(ConstData.allCharacters.asMap());
  }

  /// 加载当前选中角色的界面偏好设置
  Future<void> loadPageSettingsForCurrentCharacter() async {
    final charSetting = characterSettings[currentCharacterId.value];
    if (charSetting == null) {
      // 如果当前角色ID没有对应设置，可以设置一个默认行为
      return;
    }

    final Box settingBox = await Hive.openBox(ConstData.settingBox);
    final storedValue = settingBox.get(charSetting.pageHiveKey);
    print('当前读取的是否使用了自定义界面信息：$storedValue');

    // 从Hive读取的值可能为null，提供一个false作为默认值
    charSetting.isUsedCustomPage.value = storedValue ?? false;
  }
}
