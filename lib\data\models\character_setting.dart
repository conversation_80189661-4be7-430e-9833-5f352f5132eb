import 'package:get/get.dart';

class AICharacter {
  // 角色的id
  final int id;
  // 角色是否有自定义界面
  final bool isCustom;
  // 角色储存自定义界面的状态的键名
  final String? pageHiveKey;
  // 角色自定义界面的跳转名称，适用与GetX.toName
  final String? customPageName;
  // 上一次是否使用了自定义界面，如果使用了，则会自动沿用上次的设定，聊天的时候默认打开自定义界面
  final RxBool isUsedCustomPage = true.obs;
  // 角色的名称，用于显示卡片信息
  final String name;
  // 英文名称，用于定位到储存hive的键名
  final String nameEN;
  // 角色卡片的图片
  final String image;
  // 角色描述信息
  final String description;

  AICharacter({
    this.pageHiveKey,
    this.customPageName,
    required this.id,
    required this.isCustom,
    required this.name,
    required this.nameEN,
    required this.image,
    required this.description,
  });
}
