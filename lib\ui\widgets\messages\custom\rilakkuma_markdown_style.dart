import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;
import '../../../theme/chat_theme.dart';

class H1Builder extends MarkdownElementBuilder {
  final ChatThemeConfig themeConfig;

  H1Builder(this.themeConfig);

  @override
  Widget visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    // 根据主题配置决定H1的装饰
    BoxDecoration decoration;

    switch (themeConfig.name) {
      case 'jine':
        decoration = BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          image: const DecorationImage(
            image: NetworkImage(
                'https://pic1.imgdb.cn/item/686d01b158cb8da5c896d17d.jpg'),
            fit: BoxFit.cover,
          ),
          border: Border(
              bottom: BorderSide(color: Colors.black.withOpacity(0.1), width: 1)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            )
          ],
        );
        break;
      case 'bear':
        decoration = BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFFF5DEB3).withOpacity(0.8),
          border: Border.all(
            color: const Color(0xFFDEB887),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.brown.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            )
          ],
        );
        break;
      case 'kuromi':
        decoration = BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: const Color(0xFFE6E6FA).withOpacity(0.8),
          border: Border.all(
            color: const Color(0xFF9370DB),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.purple.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            )
          ],
        );
        break;
      default:
        decoration = BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.withOpacity(0.1),
          border: Border(
              bottom: BorderSide(color: Colors.grey.withOpacity(0.3), width: 2)),
        );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      margin: const EdgeInsets.only(top: 20, bottom: 15),
      decoration: decoration,
      child: Text(
        element.textContent,
        style: preferredStyle?.copyWith(
          shadows: themeConfig.name == 'jine' ? [
            const Shadow(
              offset: Offset(0, 1),
              blurRadius: 2.0,
              color: Color.fromRGBO(255, 255, 255, 0.7),
            ),
          ] : null,
        ),
      ),
    );
  }
}

class ThemeMarkdownStyle {
  /// 根据主题配置生成对应的Markdown样式
  static MarkdownStyleSheet getStyle(BuildContext context, ChatThemeConfig themeConfig) {
    final theme = Theme.of(context);

    // 根据主题名称定义不同的颜色配置
    late Color h2UnderlineColor;
    late Color h3Color;
    late Color blockquoteTextColor;
    late Color codeBgColor;
    late Color codeBorderColor;
    late Color mainTextColor;
    late Color italicsTextColor;
    late BoxDecoration blockquoteDecoration;

    switch (themeConfig.name) {
      case 'jine':
        h2UnderlineColor = const Color.fromRGBO(150, 35, 62, 0.5);
        h3Color = const Color.fromRGBO(212, 120, 144, 0.7);
        blockquoteTextColor = const Color(0xFF9e6675);
        codeBgColor = const Color.fromRGBO(253, 251, 247, 1);
        codeBorderColor = const Color.fromRGBO(150, 35, 62, 0.2);
        mainTextColor = const Color.fromRGBO(81, 79, 79, 1);
        italicsTextColor = const Color(0xFF867c7b);
        blockquoteDecoration = BoxDecoration(
          color: codeBgColor,
          borderRadius: BorderRadius.circular(23),
          image: const DecorationImage(
            image: NetworkImage(
                'https://pic1.imgdb.cn/item/686d01b158cb8da5c896d17d.jpg'),
            fit: BoxFit.cover,
          ),
        );
        break;
      case 'bear':
        h2UnderlineColor = const Color(0xFFDEB887);
        h3Color = const Color(0xFF8B4513);
        blockquoteTextColor = const Color(0xFF654321);
        codeBgColor = const Color(0xFFF5EFE6);
        codeBorderColor = const Color(0xFFDEB887);
        mainTextColor = const Color(0xFF8B4513);
        italicsTextColor = const Color(0xFF654321);
        blockquoteDecoration = BoxDecoration(
          color: codeBgColor,
          borderRadius: BorderRadius.circular(12),
          border: const Border(
            left: BorderSide(
              color: Color(0xFFDEB887),
              width: 4,
            ),
          ),
        );
        break;
      case 'kuromi':
        h2UnderlineColor = const Color(0xFF9370DB);
        h3Color = const Color(0xFF663399);
        blockquoteTextColor = const Color(0xFF4B0082);
        codeBgColor = const Color(0xFFF8F8FF);
        codeBorderColor = const Color(0xFF9370DB);
        mainTextColor = const Color(0xFF4B0082);
        italicsTextColor = const Color(0xFF663399);
        blockquoteDecoration = BoxDecoration(
          color: codeBgColor,
          borderRadius: BorderRadius.circular(10),
          border: const Border(
            left: BorderSide(
              color: Color(0xFF9370DB),
              width: 4,
            ),
          ),
        );
        break;
      default: // default theme
        h2UnderlineColor = theme.colorScheme.primary;
        h3Color = theme.colorScheme.primary;
        blockquoteTextColor = theme.colorScheme.onSurface;
        codeBgColor = theme.colorScheme.surfaceContainer;
        codeBorderColor = theme.colorScheme.outline;
        mainTextColor = theme.colorScheme.onSurface;
        italicsTextColor = theme.colorScheme.onSurfaceVariant;
        blockquoteDecoration = BoxDecoration(
          color: codeBgColor,
          borderRadius: BorderRadius.circular(8),
          border: Border(
            left: BorderSide(
              color: theme.colorScheme.primary,
              width: 4,
            ),
          ),
        );
    }

    // Base text style
    final baseStyle = TextStyle(
      color: mainTextColor,
      fontSize: themeConfig.fontSize,
      fontFamily: themeConfig.fontFamily,
    );

    return MarkdownStyleSheet.fromTheme(theme).copyWith(
      p: baseStyle,
      h1: baseStyle.copyWith(
        fontSize: themeConfig.fontSize + 9, // 24
        fontWeight: FontWeight.bold,
      ),
      h2: baseStyle.copyWith(
        fontSize: themeConfig.fontSize + 5, // 20
        fontWeight: FontWeight.bold,
        decorationColor: h2UnderlineColor,
        decorationThickness: 2,
        decoration: TextDecoration.underline,
      ),
      h2Padding: const EdgeInsets.symmetric(vertical: 8),
      h3: baseStyle.copyWith(
        fontSize: themeConfig.fontSize + 3, // 18
        fontWeight: FontWeight.bold,
        color: h3Color,
      ),
      h3Padding: const EdgeInsets.symmetric(vertical: 6),
      em: baseStyle.copyWith(
        fontStyle: FontStyle.italic,
        color: italicsTextColor,
      ),
      blockquote: baseStyle.copyWith(
        color: blockquoteTextColor,
      ),
      blockquotePadding: const EdgeInsets.all(12),
      blockquoteDecoration: blockquoteDecoration,
      code: baseStyle.copyWith(
        fontFamily: 'monospace',
        backgroundColor: codeBgColor.withOpacity(0.8),
      ),
      codeblockPadding: const EdgeInsets.all(15),
      codeblockDecoration: BoxDecoration(
        color: codeBgColor,
        borderRadius: BorderRadius.circular(themeConfig.bubbleRadius),
        border: Border.all(
          color: codeBorderColor,
          width: 1,
        ),
      ),
      listBullet: baseStyle.copyWith(
        color: italicsTextColor,
      ),
      listBulletPadding: const EdgeInsets.only(right: 4),
      // 禁用删除线效果 - 将 ~~~~ 语法的删除线装饰移除
      del: baseStyle.copyWith(
        decoration: TextDecoration.none, // 移除删除线装饰
      ),
    );
  }
}
